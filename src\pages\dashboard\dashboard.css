.section-title {
  color: #fff;
  /* margin-bottom: 20px; */
  font-size: 24px;
  font-weight: 600;
}

/* Main Tank Section */
.main-tank-section {
  margin-bottom: 40px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pump-water-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.pump-water-btn:hover {
  background-color: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.pump-water-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.pump-water-btn:disabled {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.main-tank-container {
  background-color: #242526;
  border-radius: 10px;
  padding: 20px;
  color: #fff;
}

.tank-dashboard-card {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  align-items: center;
}

.tank-info {
  flex: 1;
  min-width: 250px;
}

.tank-info h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #61dafb;
}

.tank-details p {
  margin-bottom: 10px;
  font-size: 16px;
}

.tank-visual {
  flex: 1;
  min-width: 250px;
  display: flex;
  justify-content: center;
}

.view-details-btn {
  display: inline-block;
  background-color: #2196f3;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  text-decoration: none;
  margin-top: 20px;
  transition: background-color 0.3s;
}

.view-details-btn:hover {
  background-color: #0d8bf2;
}

.loading-indicator,
.error-message,
.no-data-message {
  padding: 20px;
  text-align: center;
  font-size: 16px;
}

.error-message {
  color: #f44336;
}

.mt-4 {
  margin-top: 30px;
}

/* Dashboard Stats */
.dashboard_row {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* Default: 4 equal columns */
  gap: 24px;
  margin-bottom: 20px;
}

.dashboard_data_div {
  background-color: #242526;
  color: #fff;
  border-radius: 10px;
  padding: 20px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.dashboard_data_div:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* For Tablets (768px - 1024px) */
@media (max-width: 1024px) {
  .dashboard_row {
    grid-template-columns: repeat(2, 1fr); /* Switch to 2 columns */
    gap: 16px;
  }

  .tank-dashboard-card {
    flex-direction: column;
  }

  .tank-info,
  .tank-visual {
    width: 100%;
  }
}

/* For Mobile (max-width: 768px) */
@media (max-width: 768px) {
  .section-title {
    font-size: 20px;
  }

  .dashboard_row {
    grid-template-columns: 1fr; /* Single column layout */
    gap: 12px;
  }

  .tank-info h3 {
    font-size: 18px;
  }

  .tank-details p {
    font-size: 14px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .pump-water-btn {
    width: 100%;
    justify-content: center;
    padding: 12px;
  }
}
